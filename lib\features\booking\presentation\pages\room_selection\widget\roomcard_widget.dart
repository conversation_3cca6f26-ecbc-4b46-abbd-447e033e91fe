import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_localizations.dart';
import 'package:kind_ali/core/utils/safe_translation_helper.dart';
import 'package:kind_ali/features/hotel/data/models/hotel_rooms.dart';
import 'package:kind_ali/features/booking/presentation/pages/room_selection/widget/room_options_widget.dart';

class RoomcardWidget extends ConsumerStatefulWidget {
  final Room room;
  const RoomcardWidget({super.key, required this.room});

  @override
  ConsumerState<RoomcardWidget> createState() => _RoomcardWidgetState();
}

class _RoomcardWidgetState extends ConsumerState<RoomcardWidget>
    with TickerProviderStateMixin {
  bool _isAmenitiesExpanded = false;
  bool _isCardExpanded = false;
  final int _initialAmenitiesCount = 6;
  late AnimationController _cardAnimationController;
  late Animation<double> _cardAnimation;

  @override
  void initState() {
    super.initState();
    _cardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _cardAnimation = CurvedAnimation(
      parent: _cardAnimationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _cardAnimationController.dispose();
    super.dispose();
  }

  void _toggleCardExpansion() {
    setState(() {
      _isCardExpanded = !_isCardExpanded;
      if (_isCardExpanded) {
        _cardAnimationController.forward();
      } else {
        _cardAnimationController.reverse();
      }
    });
  }

  void _showImageViewer(List<ImageList> imageList) {
    // Extract URLs from ImageList objects
    final List<String> imageUrls = imageList
        .where((image) => image.url != null && image.url!.isNotEmpty)
        .map((image) => image.url!)
        .toList();

    if (imageUrls.isEmpty) {
      // Handle case where no valid URLs are found
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No images available to display')),
      );
      return;
    }

    showDialog(
      context: context,
      barrierColor: Colors.black.withOpacity(0.8),
      builder: (BuildContext dialogContext) {
        return _buildImageViewerDialog(dialogContext, imageUrls);
      },
    );
  }

  Widget _buildImageViewerDialog(BuildContext context, List<String> images) {
    final PageController pageController = PageController();
    int currentPage = 0;

    return StatefulBuilder(
      builder: (context, setState) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: EdgeInsets.zero,
          child: Stack(
            children: [
              BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
                child: Container(
                  color: Colors.black.withOpacity(0.9),
                ),
              ),
              PageView.builder(
                scrollDirection: Axis.vertical,
                controller: pageController,
                itemCount: images.length,
                onPageChanged: (index) {
                  setState(() {
                    currentPage = index;
                  });
                },
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.all(24),
                    child: Center(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: Image.network(
                          images[index],
                          fit: BoxFit.contain,
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Container(
                              height: 300,
                              decoration: BoxDecoration(
                                color: Colors.grey[900],
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: const Center(
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  );
                },
              ),
              Positioned(
                top: 50,
                right: 24,
                child: GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.6),
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white.withOpacity(0.2)),
                    ),
                    child: const Icon(
                      Icons.close_rounded,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ),
              Positioned(
                right: 24,
                top: MediaQuery.of(context).size.height / 2 -
                    (images.length * 8),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 6),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Colors.white.withOpacity(0.2)),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(images.length, (index) {
                      return AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        margin: const EdgeInsets.symmetric(vertical: 3),
                        width: currentPage == index ? 8 : 6,
                        height: currentPage == index ? 8 : 6,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: currentPage == index
                              ? Colors.white
                              : Colors.white.withOpacity(0.5),
                        ),
                      );
                    }),
                  ),
                ),
              ),
              Positioned(
                bottom: 50,
                left: 24,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Colors.white.withOpacity(0.2)),
                  ),
                  child: Text(
                    '${currentPage + 1} of ${images.length}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _toggleAmenities() {
    setState(() {
      _isAmenitiesExpanded = !_isAmenitiesExpanded;
    });
  }

  @override
  Widget build(BuildContext context) {
    final cheapestOption = widget.room.roomOptions?.isNotEmpty == true
        ? widget.room.roomOptions!.reduce((a, b) =>
            (a.fareDetail?.totalPrice ?? double.infinity) <
                    (b.fareDetail?.totalPrice ?? double.infinity)
                ? a
                : b)
        : null;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 20,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeroImageSection(),
          const SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.room.name ?? '',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 18,
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(width: 12),
                    Text(
              cheapestOption != null
                  ? '₹${cheapestOption.fareDetail?.totalPrice?.toInt() ?? 0} / night'
                  : 'Price on request',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
              ],
            ),
          ),
          // Expandable content
          SizeTransition(
            sizeFactor: _cardAnimation,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),
                  _buildRoomHeader(),
                  const SizedBox(height: 16),
                  _buildRoomProperties(),
                  const SizedBox(height: 20),
                  _buildAmenitiesSection(),
                  const SizedBox(height: 24),
                  _buildRoomOptionsSection(),
                  const SizedBox(height: 16),
                  // _buildExpandButton(),
                  // const SizedBox(height: 16),
                ],
              ),
            ),
          ),
          // Expandable button container
          _buildExpandButton(),
        ],
      ),
    );
  }

  Widget _buildExpandButton() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: InkWell(
        onTap: _toggleCardExpansion,
        child: Container(
          height: 50,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(15
              
            ),
          ),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  _isCardExpanded ? 'Show Less' : 'View Details & Book',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(width: 8),
                AnimatedRotation(
                  turns: _isCardExpanded ? 0.5 : 0,
                  duration: const Duration(milliseconds: 300),
                  child: const Icon(
                    Icons.expand_more,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeroImageSection() {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: ClipRRect(
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12),bottom:Radius.circular(12) ),
            child: Image.network(
              widget.room.imageList?.first.url ??
                  'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=400&h=200&fit=crop',
              fit: BoxFit.cover,
              width: double.infinity,
              height: 200,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: double.infinity,
                  height: 250,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                    ),
                  ),
                  child: const Icon(
                    Icons.hotel_rounded,
                    size: 48,
                    color: Colors.white,
                  ),
                );
              },
            ),
          ),
        ),
         Positioned(
          bottom: 12,
          left: 12,
          child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 6),
                      decoration: BoxDecoration(
                        // gradient: const LinearGradient(
                        //   colors: [Color(0xFFffd89b), Color(0xFF19547b)],
                        //   begin: Alignment.topLeft,
                        //   end: Alignment.bottomRight,
                        // ),
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.star_rounded,
                            size: 14,
                            color: Colors.white,
                          ),
                          SizedBox(width: 4),
                          Text(
                            '4.5',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ],
                      ),
                    ),
        ),
        Positioned(
          bottom: 12,
          right: 12,
          child: InkWell(
            onTap: () => _showImageViewer(widget.room.imageList!),
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.white.withOpacity(0.2)),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.photo_library_rounded,
                    size: 16,
                    color: Colors.white,
                  ),
                  SizedBox(width: 6),
                  Text(
                    'View All',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRoomHeader() {
    return Text(
      'Room Details',
      style: const TextStyle(
        fontWeight: FontWeight.w700,
        fontSize: 18,
        color: Color(0xFF1a1a1a),
        height: 1.3,
      ),
    );
  }

  Widget _buildRoomProperties() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: widget.room.properties!.map((property) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: const Color(0xFFf8fafc),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: const Color(0xFFe2e8f0)),
          ),
          child: Text(
            property.data ?? '',
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w500,
              color: Color(0xFF475569),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildAmenitiesSection() {
    if (widget.room.roomLevelAmenities == null ||
        widget.room.roomLevelAmenities!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'hotel.facilities.roomAmenities'.tr(context),
          style: const TextStyle(
            fontWeight: FontWeight.w700,
            fontSize: 18,
            color: Color(0xFF1a1a1a),
          ),
        ),
        const SizedBox(height: 12),
        _buildAmenitiesGrid(),
      ],
    );
  }

  Widget _buildAmenitiesGrid() {
    final amenities = widget.room.roomLevelAmenities!;
    final hasMoreAmenities = amenities.length > _initialAmenitiesCount;
    final displayedAmenities = _isAmenitiesExpanded
        ? amenities
        : amenities.take(_initialAmenitiesCount).toList();

    return Column(
      children: [
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: displayedAmenities.map((amenity) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFFecfdf5),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: const Color(0xFFd1fae5)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.check_circle_rounded,
                    size: 14,
                    color: Color(0xFF059669),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    amenity,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF065f46),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
        if (hasMoreAmenities)
          Padding(
            padding: const EdgeInsets.only(top: 12),
            child: InkWell(
              onTap: _toggleAmenities,
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: const Color(0xFF3b82f6)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      _isAmenitiesExpanded
                          ? 'Show Less'
                          : '+${amenities.length - _initialAmenitiesCount} more',
                      style: const TextStyle(
                        color: Color(0xFF3b82f6),
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(width: 6),
                    AnimatedRotation(
                      turns: _isAmenitiesExpanded ? 0.5 : 0,
                      duration: const Duration(milliseconds: 200),
                      child: const Icon(
                        Icons.keyboard_arrow_down_rounded,
                        color: Color(0xFF3b82f6),
                        size: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildRoomOptionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'hotel.detail.selectYourRoom'.tr(context),
          style: const TextStyle(
            fontWeight: FontWeight.w700,
            fontSize: 18,
            color: Color(0xFF1a1a1a),
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 320,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount: widget.room.roomOptions!.length,
            itemBuilder: (context, index) => RoomOptionsWidget(
              room: widget.room,
              roomOption: widget.room.roomOptions![index],
            ),
            separatorBuilder: (context, index) => const SizedBox(width: 12),
          ),
        ),
      ],
    );
  }
}
