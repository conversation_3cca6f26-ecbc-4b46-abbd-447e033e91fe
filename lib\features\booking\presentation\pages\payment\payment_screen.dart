import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_text_styles.dart';
import 'package:kind_ali/core/utils/string_extention_helper.dart';
import 'package:kind_ali/features/booking/presentation/providers/room_selection_notifier.dart';
import 'package:kind_ali/core/routes/app_routes.dart';
import 'package:kind_ali/shared/widgets/custombutton_widget.dart';

class PaymentScreen extends StatefulWidget {
  final double totalAmount;
  final String hotelName;
  final String roomType;
  final String checkInDate;
  final String checkOutDate;
  final List<SelectedRoomOption>? selectedRoomOptions;
  final String? guestName;
  final String? guestEmail;
  final String? guestPhone;
  final List<Map<String, String>>? additionalGuests;

  const PaymentScreen({
    Key? key,
    required this.totalAmount,
    required this.hotelName,
    required this.roomType,
    required this.checkInDate,
    required this.checkOutDate,
    this.selectedRoomOptions,
    this.guestName,
    this.guestEmail,
    this.guestPhone,
    this.additionalGuests,
  }) : super(key: key);

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  // Payment method selection
  String _selectedPaymentMethod = 'credit_card';

  // Credit card form controllers
  final _cardNumberController = TextEditingController();
  final _cardHolderController = TextEditingController();
  final _expiryDateController = TextEditingController();
  final _cvvController = TextEditingController();

  // Form key for validation
  final _formKey = GlobalKey<FormState>();

  // Processing state
  bool _isProcessing = false;

  @override
  void dispose() {
    _cardNumberController.dispose();
    _cardHolderController.dispose();
    _expiryDateController.dispose();
    _cvvController.dispose();
    super.dispose();
  }

  void _processPayment() {
    if (_selectedPaymentMethod == 'credit_card') {
      if (!_formKey.currentState!.validate()) {
        return;
      }
    }

    setState(() {
      _isProcessing = true;
    });

    // Simulate payment processing
    Future.delayed(const Duration(seconds: 2), () {
      setState(() {
        _isProcessing = false;
      });

      // Show success dialog
      _showPaymentSuccessDialog();
    });
  }

  void _showPaymentSuccessDialog() {
    
  final now = DateTime.now();
  final bookingTime = "${now.day}/${now.month}/${now.year} ${now.hour}:${now.minute.toString().padLeft(2, '0')}";
  final screenWidth = MediaQuery.of(context).size.width;
  final screenHeight = MediaQuery.of(context).size.height;

  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) => Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
      ),
      backgroundColor: Colors.white,
      elevation: 0,
      child: Container(
        width: screenWidth * 0.98,
        constraints: BoxConstraints(
          maxWidth: screenWidth > 600 ? 700 : screenWidth * 0.98,
          minWidth: screenWidth > 600 ? 600 : (screenWidth * 0.95).clamp(350.0, screenWidth * 0.98),
          maxHeight: screenHeight * 0.85, // Increased dialog height slightly
        ),
        padding: EdgeInsets.all(screenWidth < 360 ? 16 : 24),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
          // Success Icon - Minimalist
          // Container(
          //   width: screenWidth < 360 ? 60 : 80,
          //   height: screenWidth < 360 ? 60 : 80,
          //   decoration: BoxDecoration(
          //     color: const Color(0xFF10B981),
          //     shape: BoxShape.circle,
          //   ),
          //   child: Icon(
          //     Icons.check,
          //     color: Colors.white,
          //     size: screenWidth < 360 ? 30 : 40,
          //   ),
          // ),

          // SizedBox(height: screenHeight * 0.03),

          // Success Title
          Row(
            children: [
                Container(
            width: screenWidth < 360 ? 20 : 24,
            height: screenWidth < 360 ? 20 : 24,
            decoration: BoxDecoration(
              color: const Color(0xFF10B981),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.check,
              color: Colors.white,
              size: screenWidth < 360 ? 10 : 15,
            ),
          ),
              Text(
                'payment.paymentSuccessful'.tr,
                style: TextStyle(
                  fontSize: screenWidth < 360 ? 20 : 24,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF111827),
                ),
              ),
            ],
          ),

          SizedBox(height: screenHeight * 0.01),

          // Subtitle
          Text(
            'payment.bookingConfirmed'.tr,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: screenWidth < 360 ? 14 : 16,
              color: Color(0xFF6B7280),
              fontWeight: FontWeight.w400,
            ),
          ),

          SizedBox(height: screenHeight * 0.04),
          
          // Details Container
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(screenWidth < 360 ? 16 : 20),
            decoration: BoxDecoration(
              color: const Color(0xFFF9FAFB),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFFE5E7EB),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Hotel Info
                _buildDetailSection(
                  title: "Hotel Details",
                  details: [
                    _buildDetailRow("Hotel", widget.hotelName),
                    _buildDetailRow("Room", widget.roomType),
                    _buildDetailRow("Check-in", widget.checkInDate),
                    _buildDetailRow("Check-out", widget.checkOutDate),
                  ],
                ),
                
                const SizedBox(height: 20),
                
                // Payment Info
                _buildDetailSection(
                  title: "Payment Summary",
                  details: [
                    _buildDetailRow("Amount", "₹${widget.totalAmount.toStringAsFixed(2)}"),
                    _buildDetailRow("Guest", widget.guestName ?? 'user001'),
                    _buildDetailRow("Email", widget.guestEmail ?? '<EMAIL>'),
                    if (widget.guestPhone != null) 
                      _buildDetailRow("Phone", widget.guestPhone!),
                    _buildDetailRow("Booked", bookingTime),
                  ],
                ),
              ],
            ),
          ),

          SizedBox(height: screenHeight * 0.04),

          // CTA Button - Minimalist
          SizedBox(
            width: double.infinity,
            height: screenHeight < 600 ? 48 : 52,
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                AppRoutes.navigateToItinerary(
                  context,
                  bookingId: 'BK${DateTime.now().millisecondsSinceEpoch.toString().substring(5, 13)}',
                  hotelName: widget.hotelName,
                  roomType: widget.roomType,
                  checkInDate: widget.checkInDate,
                  checkOutDate: widget.checkOutDate,
                  numberOfGuests: 2,
                  totalAmount: widget.totalAmount,
                  guestName: widget.guestName ?? 'user001',
                  guestEmail: widget.guestEmail ?? '<EMAIL>',
                  guestPhone: widget.guestPhone,
                  selectedRoomOptions: widget.selectedRoomOptions,
                  additionalGuests: widget.additionalGuests,
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'payment.viewItinerary'.tr,
                style: TextStyle(
                  fontSize: screenWidth < 360 ? 14 : 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
        ),
      ),
    ),
  ));
}

Widget _buildDetailSection({
  required String title,
  required List<Widget> details,
}) {
  final screenWidth = MediaQuery.of(context).size.width;
  final screenHeight = MediaQuery.of(context).size.height;

  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        title,
        style: TextStyle(
          fontSize: screenWidth < 360 ? 12 : 14,
          fontWeight: FontWeight.w600,
          color: Color(0xFF374151),
          letterSpacing: 0.5,
        ),
      ),
      SizedBox(height: screenHeight * 0.015),
      ...details,
    ],
  );
}

Widget _buildDetailRow(String label, String value) {
  final screenWidth = MediaQuery.of(context).size.width;
  final screenHeight = MediaQuery.of(context).size.height;

  return Padding(
    padding: EdgeInsets.only(bottom: screenHeight * 0.01),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: screenWidth < 360 ? 70 : 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: screenWidth < 360 ? 12 : 14,
              color: Color(0xFF6B7280),
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        SizedBox(width: screenWidth * 0.04),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: screenWidth < 360 ? 12 : 14,
              color: Color(0xFF111827),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    ),
  );
}

// void _showPaymentSuccessDialog() {
//   final now = DateTime.now();
//   final bookingTime = "${now.day}/${now.month}/${now.year} ${now.hour}:${now.minute.toString().padLeft(2, '0')}";

//   showDialog(
//     context: context,
//     barrierDismissible: false,
//     builder: (context) => AlertDialog(
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(24),
//       ),
//       backgroundColor: Colors.white,
//       elevation: 0,
//       contentPadding: const EdgeInsets.all(32),
//       content: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           // Success Icon - Minimalist
//           Container(
//             width: 80,
//             height: 80,
//             decoration: BoxDecoration(
//               color: const Color(0xFF10B981),
//               shape: BoxShape.circle,
//             ),
//             child: const Icon(
//               Icons.check,
//               color: Colors.white,
//               size: 40,
//             ),
//           ),
          
//           const SizedBox(height: 24),
          
//           // Success Title
//           Text(
//             'payment.paymentSuccessful'.tr,
//             style: const TextStyle(
//               fontSize: 24,
//               fontWeight: FontWeight.w600,
//               color: Color(0xFF111827),
//             ),
//           ),
          
//           const SizedBox(height: 8),
          
//           // Subtitle
//           Text(
//             'payment.bookingConfirmed'.tr,
//             textAlign: TextAlign.center,
//             style: const TextStyle(
//               fontSize: 16,
//               color: Color(0xFF6B7280),
//               fontWeight: FontWeight.w400,
//             ),
//           ),
          
//           const SizedBox(height: 32),
          
//           // Details Container
//           Container(
//             width: double.infinity,
//             padding: const EdgeInsets.all(20),
//             decoration: BoxDecoration(
//               color: const Color(0xFFF9FAFB),
//               borderRadius: BorderRadius.circular(16),
//               border: Border.all(
//                 color: const Color(0xFFE5E7EB),
//                 width: 1,
//               ),
//             ),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 // Hotel Info
//                 _buildDetailSection(
//                   title: "Hotel Details",
//                   details: [
//                     _buildDetailRow("Hotel", widget.hotelName),
//                     _buildDetailRow("Room", widget.roomType),
//                     _buildDetailRow("Check-in", widget.checkInDate),
//                     _buildDetailRow("Check-out", widget.checkOutDate),
//                   ],
//                 ),
                
//                 const SizedBox(height: 20),
                
//                 // Payment Info
//                 _buildDetailSection(
//                   title: "Payment Summary",
//                   details: [
//                     _buildDetailRow("Amount", "₹${widget.totalAmount.toStringAsFixed(2)}"),
//                     _buildDetailRow("Guest", widget.guestName ?? 'user001'),
//                     _buildDetailRow("Email", widget.guestEmail ?? '<EMAIL>'),
//                     if (widget.guestPhone != null) 
//                       _buildDetailRow("Phone", widget.guestPhone!),
//                     _buildDetailRow("Booked", bookingTime),
//                   ],
//                 ),
//               ],
//             ),
//           ),
          
//           const SizedBox(height: 32),
          
//           // CTA Button - Minimalist
//           SizedBox(
//             width: double.infinity,
//             height: 52,
//             child: ElevatedButton(
//               onPressed: () {
//                 Navigator.of(context).pop();
//                 AppRoutes.navigateToItinerary(
//                   context,
//                   bookingId: 'BK${DateTime.now().millisecondsSinceEpoch.toString().substring(5, 13)}',
//                   hotelName: widget.hotelName,
//                   roomType: widget.roomType,
//                   checkInDate: widget.checkInDate,
//                   checkOutDate: widget.checkOutDate,
//                   numberOfGuests: 2,
//                   totalAmount: widget.totalAmount,
//                   guestName: widget.guestName ?? 'user001',
//                   guestEmail: widget.guestEmail ?? '<EMAIL>',
//                   guestPhone: widget.guestPhone,
//                   selectedRoomOptions: widget.selectedRoomOptions,
//                   additionalGuests: widget.additionalGuests,
//                 );
//               },
//               style: ElevatedButton.styleFrom(
//                 backgroundColor: const Color(0xFF111827),
//                 foregroundColor: Colors.white,
//                 elevation: 0,
//                 shape: RoundedRectangleBorder(
//                   borderRadius: BorderRadius.circular(12),
//                 ),
//               ),
//               child: Text(
//                 'payment.viewItinerary'.tr,
//                 style: const TextStyle(
//                   fontSize: 16,
//                   fontWeight: FontWeight.w600,
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     ),
//   );
// }

// Widget _buildDetailSection({
//   required String title,
//   required List<Widget> details,
// }) {
//   return Column(
//     crossAxisAlignment: CrossAxisAlignment.start,
//     children: [
//       Text(
//         title,
//         style: const TextStyle(
//           fontSize: 14,
//           fontWeight: FontWeight.w600,
//           color: Color(0xFF374151),
//           letterSpacing: 0.5,
//         ),
//       ),
//       const SizedBox(height: 12),
//       ...details,
//     ],
//   );
// }

// Widget _buildDetailRow(String label, String value) {
//   return Padding(
//     padding: const EdgeInsets.only(bottom: 8),
//     child: Row(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         SizedBox(
//           width: 80,
//           child: Text(
//             label,
//             style: const TextStyle(
//               fontSize: 14,
//               color: Color(0xFF6B7280),
//               fontWeight: FontWeight.w400,
//             ),
//           ),
//         ),
//         const SizedBox(width: 16),
//         Expanded(
//           child: Text(
//             value,
//             style: const TextStyle(
//               fontSize: 14,
//               color: Color(0xFF111827),
//               fontWeight: FontWeight.w500,
//             ),
//           ),
//         ),
//       ],
//     ),
//   );
// }

  // void _showPaymentSuccessDialog() {
  //   showDialog(
  //     context: context,
  //     barrierDismissible: false,
  //     builder: (context) => AlertDialog(
  //       shape: RoundedRectangleBorder(
  //         borderRadius: BorderRadius.circular(16),
  //       ),
  //       content: Column(
  //         mainAxisSize: MainAxisSize.min,
  //         children: [
  //           const SizedBox(height: 16),
  //           Container(
  //             padding: const EdgeInsets.all(16),
  //             decoration: BoxDecoration(
  //               color: Colors.green.shade50,
  //               shape: BoxShape.circle,
  //             ),
  //             child: Icon(
  //               Icons.check_circle,
  //               color: Colors.green.shade600,
  //               size: 64,
  //             ),
  //           ),
  //           const SizedBox(height: 24),
  //           Text(
  //             'payment.paymentSuccessful'.tr,
  //             style: AppTextStyles.headline3.copyWith(
  //               fontWeight: FontWeight.bold,
  //             ),
  //           ),
  //           const SizedBox(height: 16),
  //           Text(
  //             'payment.bookingConfirmed'.tr,
  //             textAlign: TextAlign.center,
  //             style: TextStyle(
  //               color: Colors.grey.shade700,
  //               fontSize: 16,
  //             ),
  //           ),
  //           const SizedBox(height: 24),
  //           CustombuttonWidget(
  //             text: 'payment.viewItinerary'.tr,
  //             backgroundColor: AppColors.primary,
  //             textColor: Colors.white,
  //             borderRadius: 8,
  //             height: 50,
  //             isFullWidth: true,
  //             onPressed: () {
  //               // Navigate to itinerary screen
  //               Navigator.of(context).pop(); // Close dialog

  //               // Navigate to itinerary screen using AppRoutes
  //               AppRoutes.navigateToItinerary(
  //                 context,
  //                 bookingId: 'BK${DateTime.now().millisecondsSinceEpoch.toString().substring(5, 13)}',
  //                 hotelName: widget.hotelName,
  //                 roomType: widget.roomType,
  //                 checkInDate: widget.checkInDate,
  //                 checkOutDate: widget.checkOutDate,
  //                 numberOfGuests: 2,
  //                 totalAmount: widget.totalAmount,
  //                 guestName: widget.guestName ?? 'user001',
  //                 guestEmail: widget.guestEmail ?? '<EMAIL>',
  //                 guestPhone: widget.guestPhone,
  //                 selectedRoomOptions: widget.selectedRoomOptions,
  //                 additionalGuests: widget.additionalGuests,
  //               );
  //             },
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    // Set global context for translations
    setGlobalContext(context);

    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final availableHeight = screenHeight - keyboardHeight;

    return Scaffold(
      appBar: AppBar(
        title: Text('payment.title'.tr),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        top: false,
        child: SizedBox(
          height: availableHeight - kToolbarHeight - MediaQuery.of(context).padding.top,
          child: Column(
            children: [
              // Booking summary - Fixed height based on screen size
              Container(
                constraints: BoxConstraints(
                  maxHeight: screenHeight * 0.25, // Max 25% of screen height
                  minHeight: 120,
                ),
                child: _buildBookingSummary(),
              ),

              // Payment methods - Flexible with proper constraints
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(
                    horizontal: screenWidth * 0.04, // 4% of screen width
                    vertical: 16,
                  ),
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: screenHeight * 0.4, // Minimum content height
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'payment.paymentMethod'.tr,
                          style: AppTextStyles.subtitle1.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: screenHeight * 0.02),

                        // Payment method options
                        _buildPaymentMethodOption(
                          'credit_card',
                          'payment.creditDebitCard'.tr,
                          Icons.credit_card,
                        ),
                        SizedBox(height: screenHeight * 0.015),
                        _buildPaymentMethodOption(
                          'google_pay',
                          'payment.googlePay'.tr,
                          Icons.g_mobiledata,
                        ),
                        SizedBox(height: screenHeight * 0.015),
                        _buildPaymentMethodOption(
                          'apple_pay',
                          'payment.applePay'.tr,
                          Icons.apple,
                        ),

                        SizedBox(height: screenHeight * 0.03),

                        // Payment form based on selected method
                        if (_selectedPaymentMethod == 'credit_card')
                          _buildCreditCardForm(),

                        if (_selectedPaymentMethod == 'paypal')
                          _buildPaypalInfo(),

                        if (_selectedPaymentMethod == 'google_pay' || _selectedPaymentMethod == 'apple_pay')
                          _buildDigitalWalletInfo(),

                        // Add bottom padding to ensure content doesn't get cut off
                        SizedBox(height: screenHeight * 0.1),
                      ],
                    ),
                  ),
                ),
              ),

              // Payment button - Fixed at bottom
              _buildPaymentButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBookingSummary() {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: screenWidth * 0.04,
        vertical: screenHeight * 0.02,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.hotelName,
                      style: AppTextStyles.subtitle1.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: screenWidth < 360 ? 14 : 16,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: screenHeight * 0.005),
                    Text(
                      widget.roomType,
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: screenWidth < 360 ? 12 : 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              SizedBox(width: screenWidth * 0.02),
              Expanded(
                flex: 2,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: screenWidth * 0.03,
                    vertical: screenHeight * 0.01,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withAlpha(20),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '₹${widget.totalAmount.toStringAsFixed(2)}',
                    style: TextStyle(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: screenWidth < 360 ? 14 : 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: screenHeight * 0.015),
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                size: screenWidth < 360 ? 14 : 16,
                color: Colors.grey.shade600,
              ),
              SizedBox(width: screenWidth * 0.02),
              Expanded(
                child: Text(
                  '${widget.checkInDate} - ${widget.checkOutDate}',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: screenWidth < 360 ? 12 : 14,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          if (widget.guestName != null)
            Padding(
              padding: EdgeInsets.only(top: screenHeight * 0.01),
              child: Row(
                children: [
                  Icon(
                    Icons.person_outline,
                    size: screenWidth < 360 ? 14 : 16,
                    color: Colors.grey.shade600,
                  ),
                  SizedBox(width: screenWidth * 0.02),
                  Expanded(
                    child: Text(
                      widget.guestName!,
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: screenWidth < 360 ? 12 : 14,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodOption(String value, String title, IconData icon) {
    final isSelected = _selectedPaymentMethod == value;

    return InkWell(
      onTap: () {
        setState(() {
          _selectedPaymentMethod = value;
        });
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
          color: isSelected ? AppColors.primary.withAlpha(20) : Colors.white,
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isSelected ? AppColors.primary.withAlpha(30) : Colors.grey.shade100,
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: isSelected ? AppColors.primary : Colors.grey.shade700,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Text(
              title,
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 16,
                color: isSelected ? AppColors.primary : Colors.black,
              ),
            ),
            const Spacer(),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: AppColors.primary,
                size: 24,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreditCardForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'payment.cardDetails'.tr,
          style: AppTextStyles.subtitle2.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // Card details container
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(10),
                blurRadius: 10,
                spreadRadius: 1,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          padding: const EdgeInsets.all(20),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Card image
                Row(
                  children: [
                    Image.asset(
                      'assets/images/credit_card.png',
                      height: 30,
                      width: 40,
                      errorBuilder: (context, error, stackTrace) => Icon(
                        Icons.credit_card,
                        size: 30,
                        color: AppColors.primary,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Image.asset(
                      'assets/images/visa.png',
                      height: 25,
                      errorBuilder: (context, error, stackTrace) => const Text(
                        'VISA',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Image.asset(
                      'assets/images/mastercard.png',
                      height: 25,
                      errorBuilder: (context, error, stackTrace) => const Text(
                        'MasterCard',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // Card number
                _buildTextField(
                  controller: _cardNumberController,
                  labelText: 'payment.cardNumber'.tr,
                  hintText: 'XXXX XXXX XXXX XXXX',
                  keyboardType: TextInputType.number,
                  prefixIcon: Icons.credit_card,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'payment.validation.enterCardNumber'.tr;
                    } else if (value.replaceAll(' ', '').length != 16) {
                      return 'payment.validation.cardNumber16Digits'.tr;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Card holder name
                _buildTextField(
                  controller: _cardHolderController,
                  labelText: 'payment.cardHolderName'.tr,
                  hintText: 'User001',
                  prefixIcon: Icons.person,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'payment.validation.enterCardHolder'.tr;
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Expiry date and CVV
                Row(
                  children: [
                    Expanded(
                      child: _buildTextField(
                        controller: _expiryDateController,
                        labelText: 'payment.expiryDate'.tr,
                        hintText: 'MM/YY',
                        keyboardType: TextInputType.number,
                        prefixIcon: Icons.calendar_today,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'payment.validation.enterExpiryDate'.tr;
                          } else if (!RegExp(r'^\d{2}/\d{2}$').hasMatch(value)) {
                            return 'payment.validation.expiryFormat'.tr;
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildTextField(
                        controller: _cvvController,
                        labelText: 'payment.cvv'.tr,
                        hintText: 'XXX',
                        keyboardType: TextInputType.number,
                        prefixIcon: Icons.lock,
                        obscureText: true,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'payment.validation.enterCvv'.tr;
                          } else if (value.length != 3) {
                            return 'payment.validation.cvv3Digits'.tr;
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Save card option
                Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: AppColors.primary,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'payment.saveCardFuture'.tr,
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Security note
                Row(
                  children: [
                    Icon(
                      Icons.security,
                      color: Colors.grey.shade600,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'payment.securePayment'.tr,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPaypalInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'payment.paypal'.tr,
          style: AppTextStyles.subtitle2.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Text(
                'payment.redirectInfo.paypal'.tr,
                style: TextStyle(
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Icon(
                    Icons.security,
                    color: Colors.grey.shade600,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'payment.redirectInfo.secureViaPaypal'.tr,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDigitalWalletInfo() {
    final isGooglePay = _selectedPaymentMethod == 'google_pay';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isGooglePay ? 'payment.googlePay'.tr : 'payment.applePay'.tr,
          style: AppTextStyles.subtitle2.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Text(
                isGooglePay ? 'payment.redirectInfo.googlePay'.tr : 'payment.redirectInfo.applePay'.tr,
                style: TextStyle(
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Icon(
                    Icons.security,
                    color: Colors.grey.shade600,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    isGooglePay ? 'payment.redirectInfo.secureViaGooglePay'.tr : 'payment.redirectInfo.secureViaApplePay'.tr,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String labelText,
    required String hintText,
    IconData? prefixIcon,
    TextInputType keyboardType = TextInputType.text,
    bool obscureText = false,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(5),
            blurRadius: 5,
            spreadRadius: 1,
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        obscureText: obscureText,
        style: const TextStyle(fontSize: 16),
        decoration: InputDecoration(
          labelText: labelText,
          hintText: hintText,
          labelStyle: TextStyle(
            color: Colors.grey.shade600,
            fontSize: 15,
          ),
          prefixIcon: prefixIcon != null
              ? Container(
                  margin: const EdgeInsets.only(left: 12, right: 8),
                  child: Icon(prefixIcon, color: AppColors.primary),
                )
              : null,
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
        validator: validator,
      ),
    );
  }

  Widget _buildPaymentButton() {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: screenWidth * 0.04,
        vertical: keyboardHeight > 0 ? screenHeight * 0.01 : screenHeight * 0.02,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: CustombuttonWidget(
          text: _isProcessing ? 'payment.processing'.tr : 'payment.payAmount'.trParams({'amount': widget.totalAmount.toStringAsFixed(2)}),
          backgroundColor: AppColors.primary,
          textColor: Colors.white,
          borderRadius: 8,
          height: screenHeight < 600 ? 48 : 56, // Smaller height for small screens
          isFullWidth: true,
          isLoading: _isProcessing,
          onPressed: () {
            if (!_isProcessing) {
              _processPayment();
            }
          },
        ),
      ),
    );
  }
}


