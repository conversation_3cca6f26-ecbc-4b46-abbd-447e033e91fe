import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:ui';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kind_ali/core/constants/app_colors.dart';
import 'package:kind_ali/core/constants/app_images.dart';
import 'package:kind_ali/core/services/location_service.dart';
import 'package:kind_ali/core/utils/location_helper.dart';
import 'package:kind_ali/core/utils/safe_translation_helper.dart';
import 'package:kind_ali/core/utils/extensions.dart';
import 'package:kind_ali/features/home/<USER>/providers/smart_location_notifier.dart';
import 'package:kind_ali/shared/widgets/location_permission_dialog.dart';
import 'package:kind_ali/features/search/data/models/recent_search.dart';
import 'package:kind_ali/core/providers/app_providers.dart';
import 'package:kind_ali/core/routes/app_routes.dart';
import 'package:kind_ali/features/home/<USER>/pages/home/<USER>/buildoffer_strip_section.dart';
import 'package:kind_ali/features/home/<USER>/pages/home/<USER>/buildtour_package_section.dart';
import 'package:kind_ali/features/home/<USER>/pages/home/<USER>/destination_section.dart';
import 'package:kind_ali/features/home/<USER>/pages/home/<USER>/special_offers_widget.dart';
import 'package:kind_ali/features/home/<USER>/pages/home/<USER>/date_selection_widget.dart';
import 'package:kind_ali/features/home/<USER>/pages/home/<USER>/guest_selection_widget.dart' as guest_widget;
import 'package:kind_ali/features/home/<USER>/pages/home/<USER>/notification_widgets.dart';
import 'package:kind_ali/features/home/<USER>/pages/home/<USER>/recent_search_widget.dart';
import 'package:kind_ali/features/hotel/presentation/pages/hotel_list/hotel_list_screen.dart';
import 'package:kind_ali/features/home/<USER>/pages/home/<USER>/search_cities_bottom_sheet.dart';
import 'package:kind_ali/features/profile/presentation/pages/language/widget/language_bottomsheet.dart';
import 'package:kind_ali/features/profile/presentation/pages/profile/profile_screen.dart';
import 'package:kind_ali/shared/widgets/arc_avatar_loader_widget.dart';
import 'package:intl/intl.dart';

class HomeScreen extends ConsumerStatefulWidget {
  final int selectedServiceIndex;
  const HomeScreen({super.key, this.selectedServiceIndex = 0});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  // final DateFormat dateFormat = DateFormat("d MMM ''yy");

  List<Map<String, dynamic>> get _services => [
        {
          'id': 'hotels',
          'svgPath': AppImages.hotel,
          'title': 'dashboard.hotels'.tr
        },
        {
          'id': 'flights',
          'svgPath': AppImages.flight,
          'title': 'dashboard.flights'.tr
        },
        {
          'id': 'carrental',
          'svgPath': AppImages.ship,
          'title': 'dashboard.ship'.tr
        },
        {
          'id': 'airporttaxi',
          'svgPath': AppImages.visa,
          'title': 'dashboard.visa'.tr
        },
      ];

  final List<List<dynamic>> offers = [
    [
      "Up to 25% Off",
      "On Domestic Hotels",
      "*Offers is Valid On UPI Transactions Only",
      "YTUPI",
      "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Kerala.jpg",
    ],
    [
      "Up to 35% OFF",
      "On International Hotels",
      "*Offers is Valid Only On Confirmed Hotel Bookings",
      "YTICICEMI",
      "https://images.via.com/static/img/general/New_UI_Images/Top_routes/goa.jpg",
    ],
    [
      "Up to 30% Off",
      "On Weekend Bookings",
      "*Offer Valid On Axis Bank Credit Card EMI Transactions Only",
      "YRAXISEMI",
      "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Dubai.png",
    ],
    [
      "Up to 40% OFF",
      "On Luxury Resorts",
      "*Valid on HDFC Bank Debit & Credit Cards",
      "YHDFC40",
      "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Maldives.png",
    ],
    [
      "Up to 20% Off",
      "On Hill Station Hotels",
      "*Weekend Special Offer Valid Till Sunday",
      "WEEKEND20",
      "https://images.via.com/static/img/general/New_UI_Images/Top_routes/Himachal.png",
    ]
  ];

  final List<List<String>> destinations = [
    [
      'Maldives',
      'https://images.unsplash.com/photo-*************-be1999ff37cd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8bWFsZGl2ZXN8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60',
    ],
    [
      'Bali',
      'https://images.unsplash.com/photo-*************-e657df975ab4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8YmFsaXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=500&q=60',
    ],
    [
      'Santorini',
      'https://images.unsplash.com/photo-*************-e3a8d69ac5ff?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8c2FudG9yaW5pfGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
    ],
    [
      'Paris',
      'https://images.unsplash.com/photo-1502602898657-3e91760cbb34?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8cGFyaXN8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60',
    ],
    [
      'Dubai',
      'https://images.unsplash.com/photo-1512453979798-5ea266f8880c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8ZHViYWl8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60',
    ]
  ];

final List<Map<String, dynamic>> popularDestinations = [
  {
    'name': 'Mountain Retreat',
    'location': 'Manali, Himachal Pradesh',
    'price': '₹8,999',
    'oldPrice': '₹11,999',
    'offer': '25% OFF',
         'image': 'https://images.unsplash.com/photo-1626621341517-bbf3d9990a23?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',

  },
  {
    'name': 'Beach Paradise',
    'location': 'Goa',
    'price': '₹12,499',
    'oldPrice': '₹15,599',
    'offer': '20% OFF',
    'image':
        'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=60',
  },
  {
    'name': 'Desert Oasis',
    'location': 'Jaisalmer, Rajasthan',
    'price': '₹10,999',
    'oldPrice': '₹13,399',
    'offer': '18% OFF',
      'image': 'https://images.unsplash.com/photo-1599661046289-e31897846e41?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',

 },
];



  int _selectedServiceIndex = 0;

  @override
  void initState() {
    super.initState();
    _selectedServiceIndex = widget.selectedServiceIndex;
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (mounted) {
        final homeNotifier = ref.read(homeProvider.notifier);
        final homeState = ref.read(homeProvider);

        // Load tourist places data
        await homeNotifier.loadUsers();

        // Set default dates if not set
        if (homeState.checkInDate == null) {
          homeNotifier.setCheckInDate(DateTime.now());
        }
        if (homeState.checkOutDate == null) {
          homeNotifier.setCheckOutDate(DateTime.now().add(const Duration(days: 1)));
        }
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget _buildServiceItemBookStyle(
      String svgPath, String title, String serviceId, bool isSelected) {
    return InkWell(
      onTap: () {
        setState(() {
          _selectedServiceIndex =
              _services.indexWhere((service) => service['id'] == serviceId);
        });
      },
      borderRadius: BorderRadius.circular(12),
      child: AnimatedContainer(
        duration: Duration(milliseconds: 200),
        curve: Curves.easeOut,
        width: 65,
        height: 65,
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : AppColors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? AppColors.white : Colors.grey.withOpacity(0.2),
            width: isSelected ? 0.5 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              svgPath,
              width: 30,
              height: 30,
              errorBuilder: (context, error, stackTrace) {
                return Icon(
                  Icons.image_not_supported,
                  color: isSelected ? AppColors.primary : Colors.grey[600],
                  size: 24,
                );
              },
            ),
            SizedBox(height: 6),
            Text(
              title,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: isSelected ? AppColors.white : AppColors.primary,
                fontSize: 9,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                height: 1.0,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Moved setGlobalContext to initState to avoid calling on every rebuild

    return Scaffold(
      backgroundColor: AppColors.background,
      body: Consumer(
        builder: (context, ref, child) {
          final homeState = ref.watch(homeProvider);
          final localizationState = ref.watch(localizationProvider);

          return SafeArea(
            child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            padding: EdgeInsets.all(2),
                            height: 50,
                            width: 120,
                            child: Image.asset(
                              AppImages.logo,
                             
                              colorBlendMode: BlendMode.srcIn,
                              fit: BoxFit.cover,
                            ),
                          ),
                          
                          Consumer(
                            builder: (context, ref, child) {
                              final authState = ref.watch(authProvider);
                              // Only show the row if user is logged in
                              if (authState.isLoggedIn) {
                                return Row(children: [
                                  IconButton(
                                    onPressed: () {
                                      Navigator.pushNamed(context, AppRoutes.profile);
                                    },
                                    icon: Icon(Icons.person, color: AppColors.primary),
                                  ),
                                  IconButton(
                                    onPressed: () {
                                      Navigator.push(context, MaterialPageRoute(builder: (context) => NotificationWidgets(),));
                                    },
                                    icon: Icon(Icons.notifications_active_outlined, color: AppColors.primary),
                                  ),
                                ],);
                              } else {
                                // Return an empty container if user is not logged in
                                return Container();
                              }
                            },
                          ),
                      
                    ]
                      ),
                      Container(
                        height: 100,
                        padding: EdgeInsets.symmetric(horizontal: 10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: _services.asMap().entries.map((entry) {
                            int index = entry.key;
                            var service = entry.value;
                            bool isSelected = index == _selectedServiceIndex;
                            return _buildServiceItemBookStyle(
                                service['svgPath'],
                                service['title'],
                                service['id'],
                                isSelected);
                          }).toList(),
                        ),
                      ),
                      _services[_selectedServiceIndex]['id'] == 'hotels'
                          ? 
                          //search widget
                          Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(20),
                                child: BackdropFilter(
                                  filter:
                                      ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                                  child: Container(
                                        padding: EdgeInsets.all(5),
                                        decoration: BoxDecoration(
                                          color: Colors.white.withAlpha(51),
                                          borderRadius:
                                              BorderRadius.circular(20),
                                          border: Border.all(
                                            color: Colors.white.withAlpha(51),
                                            width: 1.5,
                                          ),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black.withAlpha(30),
                                              blurRadius: 10,
                                              spreadRadius: 2,
                                              offset: Offset(0, 4),
                                            )
                                          ],
                                        ),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                           
                                            InkWell(
                                              onTap: () async {
                                                // Check if location permission is needed
                                                final smartLocationState = ref.read(smartLocationProvider);

                                                if (smartLocationState.needsLocationPermission()) {
                                                  // Request location permission first
                                                  final result = await LocationHelper.getLocationWithErrorHandling(
                                                    context,
                                                    showErrorDialog: true,
                                                    onRetry: () {
                                                      ref.read(smartLocationProvider.notifier).requestLocationAndUpdate();
                                                    },
                                                  );

                                                  if (result.isSuccess) {
                                                    // Update smart location with current location
                                                    ref.read(smartLocationProvider.notifier).setLocation(
                                                      result.locationName,
                                                      SmartLocationSource.currentLocation,
                                                    );
                                                  }
                                                }

                                                // Show search bottom sheet (with mounted check)
                                                if (context.mounted) {
                                                  showModalBottomSheet(
                                                    context: context,
                                                    isScrollControlled: true,
                                                    backgroundColor: Colors.transparent,
                                                    builder: (context) => SearchCitiesBottomSheet(
                                                      onCitySelected: (city) {
                                                        ref.read(homeProvider.notifier).setDestination(city);
                                                        // Update smart location with selected city
                                                        ref.read(smartLocationProvider.notifier).setLocation(
                                                          city,
                                                          SmartLocationSource.recentSearch,
                                                        );
                                                      },
                                                    ),
                                                  );
                                                }
                                              },
                                              child: Container(
                                                height: 60,
                                                decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  boxShadow: [
                                                    BoxShadow(
                                                      color: Colors.black
                                                          .withAlpha(30),
                                                      blurRadius: 8,
                                                      spreadRadius: 1,
                                                    ),
                                                  ],
                                                  border: Border.all(
                                                    color: homeState
                                                            .destinationController
                                                            .text
                                                            .isNotEmpty
                                                        ? AppColors.primary
                                                            .withAlpha(30)
                                                        : Colors.grey
                                                            .withAlpha(30),
                                                    width: 1.5,
                                                  ),
                                                ),
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        vertical: 10,
                                                        horizontal: 20),
                                                child: Row(
                                                  children: [
                                                    SizedBox(
                                                      width: 25,
                                                      height: 25,
                                                      child: Icon(
                                                        Icons
                                                            .my_location_outlined,
                                                        color: AppColors.primary,
                                                        size: 25,
                                                      ),
                                                    ),
                                                    const SizedBox(width: 15),
                                                    Expanded(
                                                      child: Consumer(
                                                        builder: (context, ref, child) {
                                                          final smartLocationState = ref.watch(smartLocationProvider);
                                                          final hasDestination = homeState.destinationController.text.isNotEmpty;

                                                          return Column(
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            mainAxisAlignment: MainAxisAlignment.center,
                                                            children: [
                                                              Text(
                                                                hasDestination
                                                                    ? homeState.destinationController.text
                                                                    : smartLocationState.getDisplayText(),
                                                                style: TextStyle(
                                                                  color: hasDestination
                                                                      ? AppColors.text
                                                                      : (smartLocationState.source == SmartLocationSource.fallback
                                                                          ? AppColors.primary
                                                                          : AppColors.text),
                                                                  fontSize: 16,
                                                                  fontWeight: FontWeight.w500,
                                                                ),
                                                              ),
                                                              // if (!hasDestination && smartLocationState.getSubtitleText() != null)
                                                              //   Text(
                                                              //     smartLocationState.getSubtitleText()!,
                                                              //     style: TextStyle(
                                                              //       color: AppColors.textLight,
                                                              //       fontSize: 12,
                                                              //       fontWeight: FontWeight.w400,
                                                              //     ),
                                                              //   ),
                                                            ],
                                                          );
                                                        },
                                                      ),
                                                    ),SizedBox(width: 20,)
                                                  ],
                                                ),
                                              ),
                                            ),
                                            const SizedBox(height: 5),
                                            InkWell(
                                              onTap: () {
                                                showModalBottomSheet(
                                                  backgroundColor:
                                                      AppColors.white,
                                                  context: context,
                                                  isScrollControlled: true,
                                                  constraints: BoxConstraints(
                                                    minHeight:
                                                        MediaQuery.of(context)
                                                                .size
                                                                .height *
                                                            0.3,
                                                    maxHeight:
                                                        MediaQuery.of(context)
                                                                .size
                                                                .height *
                                                            0.7,
                                                  ),
                                                  shape:
                                                      const RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.vertical(
                                                            top:
                                                                Radius.circular(
                                                                    20)),
                                                  ),
                                                  builder: (context) {
                                                    return const DateSelectionWidget();
                                                  },
                                                );
                                              },
                                              child: Container(
                                                height: 60,
                                                padding: const EdgeInsets.symmetric(
                                                    vertical: 10,
                                                    horizontal: 20),
                                                decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  border: homeState
                                                              .checkInDate !=
                                                          null
                                                      ? Border.all(
                                                          color: AppColors
                                                              .primary
                                                              .withAlpha(30),
                                                          width: 1.5)
                                                      : Border.all(
                                                          color: Colors.grey
                                                              .withAlpha(30),
                                                          width: 1.5),
                                                  boxShadow: [
                                                    BoxShadow(
                                                      color: Colors.black
                                      
                                                          .withAlpha(30),
                                                      blurRadius: 8,
                                                      spreadRadius: 1,
                                                    ),
                                                  ],
                                                ),
                                                child: Row(
                                                  children: [
                                                    SizedBox(
                                                      width: 25,
                                                      height: 25,
                                                      child: Icon(
                                                        Icons
                                                            .calendar_month_outlined,
                                                        size: 25,
                                                        color:
                                                            AppColors.primary,
                                                      ),
                                                    ),
                                                    SizedBox(width: 15), // Responsive spacing
                                                    Expanded( // ✅ FIX: Wrap in Expanded to prevent overflow
                                                      child: Row(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .center,
                                                        children: [
                                                          Flexible( // ✅ FIX: Make check-in section flexible
                                                            flex: 2,
                                                            child: Row(
                                                              children: [
                                                                Text(
                                                                  homeState.checkInDate !=
                                                                          null
                                                                      ? '${homeState.checkInDate!.day}'
                                                                      : '',
                                                                  style:
                                                                      TextStyle(
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                    color:
                                                                        AppColors
                                                                            .text,
                                                                    fontSize:
                                                                        context.responsiveHeight(0.031), // Responsive font size
                                                                  ),
                                                                ),
                                                                SizedBox(
                                                                  width: 4,
                                                                ),
                                                                Flexible( // ✅ FIX: Make text column flexible
                                                                  child: Column(
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment
                                                                            .center,
                                                                    crossAxisAlignment:
                                                                        CrossAxisAlignment
                                                                            .start,
                                                                    children: [
                                                                      Text(
                                                                        'search.checkin_header'
                                                                            .tr(context),
                                                                        style:
                                                                            TextStyle(
                                                                          color: homeState.checkInDate !=
                                                                                  null
                                                                              ? AppColors
                                                                                  .primary
                                                                              : Colors
                                                                                  .grey[600],
                                                                          fontSize:
                                                                              context.responsiveHeight(0.012), // Responsive font size
                                                                        ),
                                                                        overflow:
                                                                            TextOverflow
                                                                                .ellipsis,
                                                                        maxLines: 1, // ✅ FIX: Limit to 1 line
                                                                      ),
                                                                      Text(
                                                                        homeState.checkInDate !=
                                                                                null
                                                                            ?
                                                                            // '${homeState.checkInDate!.day}/${homeState.checkInDate!.month}/${homeState.checkInDate!.year}'
                                                                            DateFormat("MMM ''yy")
                                                                                .format(homeState.checkInDate!)
                                                                            : 'Select date',
                                                                        style:
                                                                            TextStyle(
                                                                          fontWeight: homeState.checkInDate !=
                                                                                  null
                                                                              ? FontWeight
                                                                                  .bold
                                                                              : FontWeight
                                                                                  .normal,
                                                                          color: homeState.checkInDate !=
                                                                                  null
                                                                              ? AppColors
                                                                                  .text
                                                                              : Colors
                                                                                  .grey,
                                                                          fontSize:
                                                                              context.responsiveHeight(0.012), // Responsive font size
                                                                        ),
                                                                        overflow:
                                                                            TextOverflow
                                                                                .ellipsis,
                                                                        maxLines: 1, // ✅ FIX: Limit to 1 line
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                          // SizedBox(width: context.responsiveWidth(0.005)), // Responsive spacing
                                                          Flexible( // ✅ FIX: Make nights container flexible
                                                            flex: 1,
                                                            child: Container(
                                                              padding:
                                                                  EdgeInsets.all(
                                                                      context.responsiveWidth(0.01)), // Responsive padding
                                                              decoration:
                                                                  BoxDecoration(
                                                                color: AppColors
                                                                    .textLight
                                                                    .withAlpha(
                                                                        30),
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            8),
                                                              ),
                                                              child: Text(
                        '${homeState.checkOutDate?.difference(homeState.checkInDate!).inDays} nights',
                        style: TextStyle(
                          fontSize: context.responsiveHeight(0.013), // Responsive font size
                          fontWeight: FontWeight.w500,
                          color: AppColors.primary,
                        ),
                        overflow: TextOverflow.ellipsis, // ✅ FIX: Add overflow handling
                        maxLines: 1, // ✅ FIX: Limit to 1 line
                      ),
                                                            ),
                                                          ),
                                                          SizedBox(width: context.responsiveWidth(0.02)), // Responsive spacing
                                                          Flexible( // ✅ FIX: Make check-out section flexible
                                                            flex: 2,
                                                            child: Row(
                                                              children: [
                                                                Text(
                                                                  homeState.checkOutDate !=
                                                                          null
                                                                      ? '${homeState.checkOutDate!.day}'
                                                                      : '',
                                                                  style:
                                                                      TextStyle(
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                    color:
                                                                        AppColors
                                                                            .text,
                                                                    fontSize:
                                                                        context.responsiveHeight(0.032), // Responsive font size
                                                                  ),
                                                                ),
                                                                SizedBox(
                                                                  width: 4,
                                                                ),
                                                                Flexible( // ✅ FIX: Make text column flexible
                                                                  child: Column(
                                                                    mainAxisAlignment:
                                                                        MainAxisAlignment
                                                                            .center,
                                                                    crossAxisAlignment:
                                                                        CrossAxisAlignment
                                                                            .start,
                                                                    children: [
                                                                      Text(
                                                                        'search.checkout_header'
                                                                            .tr(context),
                                                                        style:
                                                                            TextStyle(
                                                                          color: homeState.checkInDate !=
                                                                                  null
                                                                              ? AppColors
                                                                                  .primary
                                                                              : Colors
                                                                                  .grey[600],
                                                                          fontSize:
                                                                              context.responsiveHeight(0.012), // Responsive font size
                                                                        ),
                                                                        overflow:
                                                                            TextOverflow
                                                                                .ellipsis,
                                                                        maxLines: 1, // ✅ FIX: Limit to 1 line
                                                                      ),
                                                                      Text(
                                                                        homeState.checkOutDate !=
                                                                                null
                                                                            ?
                                                                            //  '${homeState.checkOutDate!.day}/${homeState.checkOutDate!.month}/${homeState.checkOutDate!.year}'
                                                                            DateFormat("MMM ''yy")
                                                                                .format(homeState.checkOutDate!)
                                                                            : 'Select date',
                                                                        style:
                                                                            TextStyle(
                                                                          fontWeight: homeState.checkOutDate !=
                                                                                  null
                                                                              ? FontWeight
                                                                                  .bold
                                                                              : FontWeight
                                                                                  .normal,
                                                                          color: homeState.checkOutDate !=
                                                                                  null
                                                                              ? AppColors
                                                                                  .text
                                                                              : Colors
                                                                                  .grey,
                                                                          fontSize:
                                                                              context.responsiveHeight(0.012), // Responsive font size
                                                                        ),
                                                                        overflow:
                                                                            TextOverflow
                                                                                .ellipsis,
                                                                        maxLines: 1, // ✅ FIX: Limit to 1 line
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            const SizedBox(height: 5),
                                            InkWell(
                                              onTap: () {
                                                showModalBottomSheet(
                                                  backgroundColor:
                                                      AppColors.white,
                                                  context: context,
                                                  isScrollControlled: true,
                                                  constraints: BoxConstraints(
                                                    minHeight:
                                                        MediaQuery.of(context)
                                                                .size
                                                                .height *
                                                            0.3,
                                                    maxHeight:
                                                        MediaQuery.of(context)
                                                                .size
                                                                .height *
                                                            0.7,
                                                  ),
                                                  shape:
                                                      const RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.vertical(
                                                            top:
                                                                Radius.circular(
                                                                    20)),
                                                  ),
                                                  builder: (context) {
                                                    return const guest_widget.GuestSelectionWidget();
                                                  },
                                                );
                                              },
                                              child: Container(
                                                height: 60,
                                                padding: const EdgeInsets.symmetric(
                                                    vertical: 10,
                                                    horizontal: 20),
                                                decoration: BoxDecoration(
                                                  color: Colors.white,
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  border: Border.all(
                                                    color: AppColors.primary
                                                        .withAlpha(30),
                                                    width: 1.5,
                                                  ),
                                                  boxShadow: [
                                                    BoxShadow(
                                                      color: Colors.black
                                                          .withAlpha(30),
                                                      blurRadius: 8,
                                                      spreadRadius: 1,
                                                    ),
                                                  ],
                                                ),
                                                child: Row(
                                                  children: [
                                                    SizedBox(
                                                      width: 25,
                                                      height: 25,
                                                      child: Icon(
                                                        Icons.person_outline,
                                                        size: 25,
                                                        color:
                                                            AppColors.primary,
                                                      ),
                                                    ),
                                                    const SizedBox(width:15),
                                                    Expanded(
                                                      child: Text(
                                                        _getGuestText(homeState),
                                                        style: TextStyle(
                                                          color: homeState.rooms.isNotEmpty
                                                              ? AppColors.text
                                                              : AppColors.primary,
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            SizedBox(height: 5),
                                            Material(
                                              color: Colors.transparent,
                                              elevation: 4,
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              child: GestureDetector(
                                                onTap: () async {
                                                  final isValid = ref.read(homeProvider.notifier)
                                                      .isSearchValid();
                                                  if (!isValid) {
                                                    ScaffoldMessenger.of(
                                                            context)
                                                        .showSnackBar(
                                                      SnackBar(
                                                        content: const Text(
                                                            'Please select check-in and check-out dates'),
                                                        backgroundColor:
                                                            Colors.red,
                                                        behavior:
                                                            SnackBarBehavior
                                                                .floating,
                                                        shape:
                                                            RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(10),
                                                        ),
                                                      ),
                                                    );
                                                    return;
                                                  }

                                                  // Save current search to recent searches
                                                  if (homeState.destinationController.text.isNotEmpty &&
                                                      homeState.checkInDate != null &&
                                                      homeState.checkOutDate != null) {
                                                    // Use HomeNotifier RoomData directly
                                                    final convertedRooms = homeState.rooms;

                                                    final newSearch = RecentSearch(
                                                      destination: homeState.destinationController.text,
                                                      checkInDate: homeState.checkInDate!,
                                                      checkOutDate: homeState.checkOutDate!,
                                                      rooms: convertedRooms,
                                                      searchTimestamp: DateTime.now(),
                                                    );
                                                    await ref.read(homeProvider.notifier).addRecentSearch(newSearch);
                                                  }

                                                  if (context.mounted) {
                                                    // showDialog(
                                                    //   context: context,
                                                    //   barrierDismissible: false,
                                                    //   builder: (_) => const Dialog(
                                                    //       backgroundColor:
                                                    //           Colors.transparent,
                                                    //       elevation: 0,
                                                    //       child: StaggeredDotsAvatarLoader(
                                                    //           image: AssetImage(
                                                    //               AppImages
                                                    //                   .logo,))),
                                                    // );

                                                    // await Future.delayed(
                                                    //     const Duration(
                                                    //         seconds: 3));

                                                    // if (context.mounted) {
                                                    //   Navigator.of(context).pop();
                                                    // }

                                                    if (context.mounted) {
                                                      Navigator.push(
                                                        context,
                                                        MaterialPageRoute(
                                                          builder: (context) =>
                                                              const HotelListScreen(),
                                                        ),
                                                      );
                                                    }
                                                  }
                                                },
                                                behavior:
                                                    HitTestBehavior.opaque,
                                                child: Container(
                                                  height: 60,
                                                  width: double.infinity,
                                                  padding: const EdgeInsets.symmetric(
                                                      horizontal: 20,
                                                      vertical: 10),
                                                  decoration: BoxDecoration(
                                                    color: AppColors.primary,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            12),
                                                  ),
                                                  child: Center(
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        Text(
                                                          'search.button'.tr(context),
                                                          style: TextStyle(
                                                            color: Colors.white,
                                                            fontSize: 16,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                        ),
                                                        SizedBox(width: 8),
                                                        Icon(
                                                          Icons.search,
                                                          color: Colors.white,
                                                          size: 20,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                ),
                              ),
                            )
                          : Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(20),
                                child: BackdropFilter(
                                  filter:
                                      ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                                  child: Container(
                                      padding: EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                        color: Colors.white
                                            .withAlpha(51), // 0.2 * 255 = ~51
                                        borderRadius: BorderRadius.circular(20),
                                        border: Border.all(
                                          color: Colors.white
                                              .withAlpha(51), // 0.2 * 255 = ~51
                                          width: 1.5,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withAlpha(30),
                                            blurRadius: 10,
                                            spreadRadius: 2,
                                            offset: Offset(0, 4),
                                          ),
                                        ],
                                      ),
                                      child: Container(
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(20)),
                                          child: Image.asset(
                                              'assets/images/Screenshot 2025-05-29 184713.png'))),
                                ),
                              ),
                            ),
                      SizedBox(height: 5),
                      _services[_selectedServiceIndex]['id'] == 'hotels'
                          ? Column(
                              children: [
                                // Recent Search Widget
                                
                                const RecentSearchWidget(),

                                Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'search.special_offers'.tr(context),
                                    style: TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  SizedBox(
                                    height: 140,
                                    child: ListView.builder(
                                      scrollDirection: Axis.horizontal,
                                      itemCount: offers.length,
                                      itemBuilder: (context, index) {
                                        final item = offers[index];
                                        return Container(
                                          width: 360,
                                          margin: EdgeInsets.only(right: 10),
                                          child: BuildofferStripSection(
                                            title: item[0],
                                            subtitle: item[1],
                                            promoCode: item[3],
                                            description: item[2],
                                            image: item[4],
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Container(
                                    width: double.infinity,
                                    height: 80,
                                    margin: EdgeInsets.symmetric(vertical: 0),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                        colors: [
                                          Color(0xFF8A2387),
                                          Color(0xFFE94057),
                                          Color(0xFFF27121),
                                        ],
                                      ),
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: [
                                        BoxShadow(
                                          color:
                                              Color(0xFFE94057).withAlpha(60),
                                          blurRadius: 12,
                                          spreadRadius: 2,
                                          offset: Offset(0, 6),
                                        ),
                                      ],
                                      border: Border.all(
                                        color: Colors.white.withAlpha(30),
                                        width: 0.5,
                                      ),
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(20),
                                      child: Stack(
                                        children: [
                                          Opacity(
                                            opacity: 0.05,
                                            child: Container(
                                              width: double.infinity,
                                              height: double.infinity,
                                              decoration: BoxDecoration(
                                                image: DecorationImage(
                                                  image: NetworkImage(
                                                      'https://www.transparenttextures.com/patterns/diamond-upholstery.png'),
                                                  repeat: ImageRepeat.repeat,
                                                ),
                                              ),
                                            ),
                                          ),
                                          Positioned(
                                            top: -20,
                                            right: -20,
                                            child: Container(
                                              width: 80,
                                              height: 80,
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                gradient: RadialGradient(
                                                  colors: [
                                                    Colors.white.withAlpha(50),
                                                    Colors.white.withAlpha(0),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                          Positioned(
                                            bottom: -25,
                                            left: 30,
                                            child: Container(
                                              width: 60,
                                              height: 60,
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                gradient: RadialGradient(
                                                  colors: [
                                                    Colors.white.withAlpha(30),
                                                    Colors.white.withAlpha(0),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                          Positioned(
                                            top: 0,
                                            left: 20,
                                            right: 20,
                                            child: Container(
                                              height: 2,
                                              decoration: BoxDecoration(
                                                gradient: LinearGradient(
                                                  begin: Alignment.centerLeft,
                                                  end: Alignment.centerRight,
                                                  colors: [
                                                    Colors.transparent,
                                                    Colors.amber.shade200,
                                                    Colors.amber.shade400,
                                                    Colors.amber.shade200,
                                                    Colors.transparent,
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 16.0,
                                                vertical: 8.0),
                                            child: Row(
                                              children: [
                                                Expanded(
                                                  flex: 3,
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      Text(
                                                        'Get 30% Off on Your First Booking',
                                                        style: TextStyle(
                                                          color: Colors.white,
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          letterSpacing: 0.5,
                                                          shadows: [
                                                            Shadow(
                                                              color: Colors
                                                                  .black
                                                                  .withAlpha(
                                                                      100),
                                                              blurRadius: 2,
                                                              offset:
                                                                  Offset(1, 1),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                      SizedBox(height: 4),
                                                      Container(
                                                        padding: EdgeInsets
                                                            .symmetric(
                                                                horizontal: 12,
                                                                vertical: 4),
                                                        decoration:
                                                            BoxDecoration(
                                                          color: Colors.white,
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(16),
                                                          boxShadow: [
                                                            BoxShadow(
                                                              color: Colors
                                                                  .black
                                                                  .withAlpha(
                                                                      30),
                                                              blurRadius: 4,
                                                              offset:
                                                                  Offset(0, 2),
                                                            ),
                                                          ],
                                                        ),
                                                        child: Text(
                                                          'Use Code: FIRST30',
                                                          style: TextStyle(
                                                            color: Color(
                                                                0xFF8A2387),
                                                            fontWeight:
                                                                FontWeight.bold,
                                                            fontSize: 10,
                                                            letterSpacing: 0.5,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                Container(
                                                  width: 50,
                                                  height: 50,
                                                  decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    color: Colors.white,
                                                    boxShadow: [
                                                      BoxShadow(
                                                        color: Colors.black
                                                            .withAlpha(30),
                                                        blurRadius: 6,
                                                        offset: Offset(0, 3),
                                                      ),
                                                    ],
                                                    border: Border.all(
                                                      color:
                                                          Colors.amber.shade200,
                                                      width: 1,
                                                    ),
                                                  ),
                                                  child: Center(
                                                    child: Icon(
                                                      Icons.card_giftcard,
                                                      size: 28,
                                                      color: Color(0xFFE94057),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'search.popular_destinations'.tr(context),
                                    style: TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  SizedBox(
                                   height: context.responsiveHeight(0.22), // 22% of screen height
                                    child: ListView.separated(
                                      itemCount: destinations.length,
                                      scrollDirection: Axis.horizontal,
                                      itemBuilder: (context, index) {
                                        final item = destinations[index]; 
                                        return DestinationSection(
                                            name: item[0], imageUrl: item[1]);
                                      },
                                      separatorBuilder: (context, index) =>
                                          SizedBox(width: 8),
                                    ),
                                  ),
                                  const SizedBox(height: 24),
                                  Text(
                                    'search.tour_packages'.tr(context),
                                    style: TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    )),SizedBox(height: 12),
                                  SizedBox(
                                    height: 680,
                                    child: ListView.separated(
                                      shrinkWrap: true,
                                        physics: NeverScrollableScrollPhysics(),
                                        itemBuilder: (context, index) {
                                          final item = popularDestinations[index];
                                          return BuildtourPackageSection(
                                              name: item['name'] ?? 'Package Name',
                                              location: item['location'] ?? 'Location',
                                              price: item['price'] ?? '₹0',
                                              originalPrice: item['oldPrice'] ?? '₹0',
                                              discount: item['offer'] ?? '0% OFF',
                                              imageUrl: item['image'] ?? 'https://via.placeholder.com/400x200?text=No+Image');
                                        },
                                        separatorBuilder: (context, index) =>
                                            const SizedBox(height: 16),
                                        itemCount: popularDestinations.length),
                                  ),
                                    ],
                                  ),
                                ),
                              ],
                            )
                          : SizedBox(),
                    ],
                  ),
                ),
              );
        },
      ),
    );
  }

  /// Get formatted guest text from rooms data
  String _getGuestText(HomeState homeState) {
    if (homeState.rooms.isEmpty) {
      return 'Number of guests';
    }

    // Calculate total guests
    int totalAdults = 0;
    int totalChildren = 0;

    for (var room in homeState.rooms) {
      totalAdults += room.adults;
      totalChildren += room.children;
    }

    // Format guest information for display
    String guestInfo = '$totalAdults ${totalAdults == 1 ? 'search.travelers.adult'.tr(context) : 'search.travelers.adults'.tr(context)}';
    if (totalChildren > 0) {
      guestInfo +=
          ', $totalChildren ${totalChildren == 1 ? 'search.travelers.child'.tr(context) : 'search.travelers.children'.tr(context)}';
    }
    guestInfo += ' · ${homeState.rooms.length} ${homeState.rooms.length == 1 ? 'search.travelers.room'.tr(context) : 'search.travelers.rooms'.tr(context)}';

    return guestInfo;
  }
}
