import 'package:flutter/material.dart';
import 'package:kind_ali/core/constants/app_localizations.dart';

// Global BuildContext variable (needs to be set) - DEPRECATED, use trWith(context) instead
BuildContext? _globalContext;

// Extension on String for easy translation
extension StringTranslation on String {


  // Basic translation - DEPRECATED, use trWith(context) instead
  String get tr {
    if (_globalContext == null) {
      debugPrint('Global context is null. Make sure to call setGlobalContext()');
      return '** $this **';
    }
    // Add context validation to prevent stale context usage
    try {
      if (_globalContext!.mounted) {
        return AppLocalizations.of(_globalContext!)?.translate(this) ?? '** $this **';
      } else {
        debugPrint('Global context is not mounted. Using fallback for: $this');
        return '** $this **';
      }
    } catch (e) {
      debugPrint('Error accessing global context for translation: $e');
      return '** $this **';
    }
  }
  
  // Translation with parameters
  String trParams(Map<String, String> params) {
    String translation = tr;
    params.forEach((key, value) {
      translation = translation.replaceAll('{$key}', value);
    });
    return translation;
  }
  
  // Plural translation (fixed version)
  String trPlural(int count) {
    if (_globalContext == null) {
      debugPrint('Global context is null. Make sure to call setGlobalContext()');
      return '** $this **';
    }
    
    final localizations = AppLocalizations.of(_globalContext!);
    if (localizations == null) return '** $this **';
    
    String translationKey;
    if (count == 0) {
      translationKey = '$this.zero';
    } else if (count == 1) {
      translationKey = '$this.one';
    } else {
      translationKey = '$this.other';
    }
    
    // Get the translation and replace the count parameter
    String translation = localizations.translate(translationKey) ?? '** $translationKey **';
    return translation.replaceAll('{count}', count.toString());
  }
  
  // Translation with specific context (alternative approach)
  String trWith(BuildContext context) {
    return AppLocalizations.of(context)?.translate(this) ?? '** $this **';
  }
}

// Helper function to set global context - DEPRECATED, avoid using
void setGlobalContext(BuildContext context) {
  // Only update if the new context is mounted and different
  if (context.mounted && _globalContext != context) {
    _globalContext = context;
  }
}

// Helper function to clear global context when widget is disposed
void clearGlobalContext() {
  _globalContext = null;
}

// Safe helper to check if global context is valid
bool isGlobalContextValid() {
  return _globalContext != null && _globalContext!.mounted;
}

// Alternative approach using a singleton pattern
class TranslationHelper {
  static TranslationHelper? _instance;
  static BuildContext? _context;
  
  TranslationHelper._();
  
  static TranslationHelper get instance {
    _instance ??= TranslationHelper._();
    return _instance!;
  }
  
  static void setContext(BuildContext context) {
    _context = context;
  }
  
  static String translate(String key) {
    if (_context == null) {
      debugPrint('Translation context is null');
      return '** $key **';
    }
    return AppLocalizations.of(_context!)?.translate(key) ?? '** $key **';
  }
  
  // Helper method for translations with parameters
  static String translateWithParams(String key, Map<String, String> params) {
    String translation = translate(key);
    params.forEach((paramKey, value) {
      translation = translation.replaceAll('{$paramKey}', value);
    });
    return translation;
  }
}

// Extension using singleton approach
extension StringTranslationSingleton on String {
  String get trs => TranslationHelper.translate(this);
  
  // Translation with parameters using singleton
  String trsParams(Map<String, String> params) {
    return TranslationHelper.translateWithParams(this, params);
  }
}